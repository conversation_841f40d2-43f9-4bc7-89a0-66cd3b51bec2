# Benchmark Controller Implementation

## Overview

This document describes the implementation of the database-driven `organisation` method in the `BenchmarkController` that replaces the static JSON file approach.

## Changes Made

### 1. Updated BenchmarkController

**File**: `app/Http/Controllers/Api/V1/RiskInsights/BenchmarkController.php`

**Key Changes**:
- Added database queries to fetch organization locations with related data
- Implemented attribute mapping from full names to abbreviations
- Added monthly risk score chart data generation
- Added error handling for missing tables/data
- Maintained the same JSON structure as the original static file

### 2. Attribute Mapping

The controller maps full attribute names to their JSON abbreviations:

```php
private const ATTRIBUTE_MAPPING = [
    'construction and exposures' => 'c_e',
    'occupancy hazards and process safety' => 'oh',
    'management safe systems of work' => 'SSoW',
    'fire detection and protection' => 'fd_p',
    'security' => 'security',
    'utilities' => 'utilities',
    'special perils' => 'sp',
    'financial business risk exposure' => 'fr',
];
```

### 3. Database Tables Used

- **OrganisationLocations**: Main location data (name, postcode, TIV)
- **Organisation**: Premium and other organization data
- **RgLocationGrading**: Risk grading scores for attributes
- **LocationMonthlyScore**: Monthly risk scores for chart data (optional)

### 4. Data Sources

The implementation queries data from:
1. `organisation_locations` table for basic location information
2. `rg_location_gradings` table for attribute scores
3. `rg_locations_monthly_score` table for monthly chart data (with fallback)
4. `organisations` table for premium data

### 5. Fallback Mechanisms

- **Missing Monthly Scores**: Generates random demo data if no monthly scores exist
- **Missing Attribute Scores**: Returns "0" for missing attributes
- **Database Errors**: Gracefully handles table/connection issues
- **Missing Segment/TCO**: Uses intelligent defaults based on location names

## API Endpoint

**URL**: `GET /api/v1/risk-insights/benchmarking/organisation`

**Response Structure**:
```json
{
    "locations": [
        {
            "location": "Location Name",
            "postcode": "AB1 2CD",
            "c_e": "30",
            "oh": "88",
            "SSoW": "63",
            "fd_p": "16",
            "tiv": "5000000",
            "premium": "94021",
            "segment": "Warehousing",
            "tco": "Cold Stores",
            "security": "41",
            "utilities": "17",
            "sp": "39",
            "fr": "49",
            "risk_score": {
                "chart_data": [
                    {"month": "Apr", "value": 35},
                    {"month": "May", "value": 42},
                    ...
                ],
                "risk_league_rating": {
                    "value": 57
                }
            }
        }
    ]
}
```

## Testing

A test file has been created at `tests/Feature/BenchmarkControllerTest.php` to verify:
- Correct JSON structure is returned
- Endpoint handles empty database gracefully
- All required fields are present

## Future Improvements

1. **Segment/TCO Mapping**: Create proper database tables for segment and TCO mappings
2. **Monthly Scores Table**: Create the `rg_locations_monthly_score` table if needed
3. **Caching**: Add caching for better performance
4. **Filtering**: Add query parameters for filtering locations
5. **Pagination**: Add pagination for large datasets

## Migration Requirements

If the `rg_locations_monthly_score` table doesn't exist, you may need to create it:

```sql
CREATE TABLE rg_locations_monthly_score (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    organisation_location_id BIGINT UNSIGNED NOT NULL,
    coverage DATE NOT NULL,
    score DECIMAL(5,2) NOT NULL,
    created_at TIMESTAMP NULL DEFAULT NULL,
    updated_at TIMESTAMP NULL DEFAULT NULL,
    FOREIGN KEY (organisation_location_id) REFERENCES organisation_locations(id)
);
```
