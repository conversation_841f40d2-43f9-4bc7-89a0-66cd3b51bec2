<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class BenchmarkControllerTest extends TestCase
{
    /**
     * Test the organisation benchmarking endpoint returns valid JSON structure
     */
    public function test_organisation_benchmarking_endpoint_returns_valid_structure()
    {
        $response = $this->get('/api/v1/risk-insights/benchmarking/organisation');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'locations' => [
                '*' => [
                    'location',
                    'postcode',
                    'tiv',
                    'premium',
                    'segment',
                    'tco',
                    'c_e',
                    'oh',
                    'SSoW',
                    'fd_p',
                    'security',
                    'utilities',
                    'sp',
                    'fr',
                    'risk_score' => [
                        'chart_data' => [
                            '*' => [
                                'month',
                                'value'
                            ]
                        ],
                        'risk_league_rating' => [
                            'value'
                        ]
                    ]
                ]
            ]
        ]);
    }

    /**
     * Test that the endpoint returns data even when database is empty
     */
    public function test_organisation_benchmarking_handles_empty_database()
    {
        $response = $this->get('/api/v1/risk-insights/benchmarking/organisation');

        $response->assertStatus(200);
        
        $data = $response->json();
        $this->assertIsArray($data['locations']);
        
        // Should return empty array if no locations exist
        if (empty($data['locations'])) {
            $this->assertEmpty($data['locations']);
        } else {
            // If locations exist, verify structure
            foreach ($data['locations'] as $location) {
                $this->assertArrayHasKey('location', $location);
                $this->assertArrayHasKey('risk_score', $location);
                $this->assertArrayHasKey('chart_data', $location['risk_score']);
                $this->assertIsArray($location['risk_score']['chart_data']);
            }
        }
    }
}
